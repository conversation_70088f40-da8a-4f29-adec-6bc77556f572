//------------------------------------------------------------------------------
//  This Source Code Form is subject to the terms of the Mozilla Public
//  License, v. 2.0. If a copy of the MPL was not distributed with this
//  file, You can obtain one at http://mozilla.org/MPL/2.0/.
//------------------------------------------------------------------------------
/// @file
/// <AUTHOR>
/// @brief Defines the OpenRP1210 API.
//------------------------------------------------------------------------------
#ifndef OPENRP1210_H__
#define OPENRP1210_H__

#define RP1210_VERSION_A 1
#define RP1210_VERSION_B 2
#define RP1210_VERSION_C 3

#ifndef RP1210_VERSION
	#define RP1210_VERSION RP1210_VERSION_C
#endif

#if defined _WIN32 || defined _WIN64
	#ifdef OpenRP1210Export
		#define OpenRP1210API __declspec(dllexport)
	#else
		#define OpenRP1210API __declspec(dllimport)
	#endif
#else
    #define OpenRP1210API
#endif

#ifdef __cplusplus
	#define CLINK extern "C"
#else
	#define CLINK 
#endif

/////////////////////////////////////////////////////////////////////////////////
/// @brief An opaque handle for API managed objects.
/////////////////////////////////////////////////////////////////////////////////
typedef void *ORP_HANDLE;

/////////////////////////////////////////////////////////////////////////////////
/// @brief Used by the API to communicate error conditions.
/////////////////////////////////////////////////////////////////////////////////
typedef int ORP_ERR;

#define ORP_ERR_NO_ERROR 0
#define ORP_ERR_BAD_ARG -1
#define ORP_ERR_MEM_ALLOC -2
#define ORP_ERR_FILE_NOT_FOUND -3
#define ORP_ERR_FILE_IO -4
#define ORP_ERR_BAD_RANGE -5
#define ORP_ERR_GENERAL -6
#define ORP_ERR_INI_INVALID_SECTION -7
#define ORP_ERR_INI_INVALID_SYMBOL -8
#define ORP_ERR_INI_INVALID_KEYNAME -9
#define ORP_ERR_INI_KEYNOTFOUND -10
#define ORP_ERR_INI_MISSING_KEYVALUE -11
#define ORP_ERR_INI_SECTION_NOT_FOUND -12
#define ORP_ERR_SYSTEM -13
#define ORP_ERR_LENGTH -14

#define ORP_IS_ERR(e) (e < ORP_ERR_NO_ERROR)

struct S_RP1210Context; // forward declaration, formally declared in RP1210.h

/////////////////////////////////////////////////////////////////////////////////
/// @brief Describes an error encountered while loading API information from
///        an RP1210 INI file.
///
/// Any errors encountered while trying to read API information from the vendor
/// RP1210 API INI file are captured with S_RP1210ImplLoadErr_t.
/////////////////////////////////////////////////////////////////////////////////
typedef struct S_RP1210ImplLoadErr_t
{
	char *ImplName;    ///< The name of the implementation that caused the error.
	char *Description; ///< A textual description of the error.
	ORP_ERR Error;     ///< The API error code representing the error.
}S_RP1210ImplLoadErr;

/////////////////////////////////////////////////////////////////////////////////
/// @brief Represents the information found in the VendorInformation section of
///        a RP1210 API vendor INI file.
/////////////////////////////////////////////////////////////////////////////////
typedef struct S_RP1210VendorInformation_t
{
	char *Name;             ///< Name of vendor, and VDA being supported.
	char *Address1;         ///< First line of vendor mailing address.
	char *Address2;         ///< Second line of vendor mailing address.
	char *City;             ///< City of vendor mailing address.
	char *State;            ///< State of vendor mailing address.
	char *Country;          ///< Country of vendor mailing address.
	char *Postal;           ///< Postal or zipcode of vendor mailing address.
	char *Telephone;        ///< Telephone number to reach support.
	char *Fax;              ///< Fax number of vendor.
	char *VendorURL;        ///< HTML link to vendor web site.
	char *MessageString;    ///< Unique message string for the vendor DLL.
	char *ErrorString;      ///< Unique error string for the vendor DLL.
	char *TimestampWeight;  ///< Weight, per bit, in microseconds of the timestamp.
	char *Devices;          ///< Comma delimited list of device IDs supported by this vendor API.
	char *Protocols;        ///< Comma delimited list of protocols IDs supported by this vendor API.

	#if RP1210_VERSION >= RP1210_VERSION_B
		char *AutoDetectCapable;      ///< If API were capable of validating that the devices in this INI file are physically attached to the computer, the string would be "yes".
		char *Version;                ///< The API version installed.
		char *RP1210;                 ///< Denotes the latest version of RP1210 supported (A, B, C, etc).
		char *DebugLevel;             ///< Controls the amount of debug information generated by the API.
		char *DebugFile;              ///< The absolute path to the file that will receive debug information generated by the API.
		char *DebugMode;              ///< Describes how debug information will be written to DebugFile.
		char *DebugFileSize;          ///< Maximum size in kilobytes that the debug file should be.
		char *NumberOfRTSCTSSessions; ///< An integer representing the number of concurrent J1939 RTS/CTS transport sessions that the API supports per client.
	#endif
	#if RP1210_VERSION >= RP1210_VERSION_C
		char *CANFormatsSupported;      ///< The CAN formats, as used by RP1210_ClientConnect, supported by the API.
		char *J1939FormatsSupported;    ///< The J1939 formats, as used by RP1210_ClientConnect, supported by the API.
		char *J1939Addresses;           ///< The number of J1939 addresses supported by the API.
		char *CANAutoBaud;              ///< Describes if "PROTOCOL:Baud-Auto" (RP1210_ClientConnect) is supported by the API.
		char *J1708FormatsSupported;    ///< The J1708 formats, as used by RP1210_ClientConnect, supported by the API.
		char *ISO15765FormatsSupported; ///< The Iso15765 formats, as used by RP1210_ClientConnect, supported by the API.
	#endif
}S_RP1210VendorInformation;

/////////////////////////////////////////////////////////////////////////////////
/// @brief Represents the information found in the DeviceInformation section of
///        a RP1210 API vendor INI file. 
/////////////////////////////////////////////////////////////////////////////////
typedef struct S_RP1210DeviceInformation_t
{
	char *DeviceID;              ///< The device identifier.
	char *DeviceDescription;     ///< The description of the device.
	char *DeviceName;            ///< The device name.
	char *DeviceParams;          ///< Vendor specific device parameters.
	char *MultiCANChannels;      ///< The number of simultaneous CAN channels supported for the device.
	char *MultiJ1939Channels;    ///< The number of simultaneous J1939 channels supported for the device.
	char *MultiISO15765Channels; ///< The number of simultaneous ISO15765 channels supported for the device.
}S_RP1210DeviceInformation;

/////////////////////////////////////////////////////////////////////////////////
/// @brief Represents the information found in the ProtocolInformation section of
///        a RP1210 API vendor INI file. 
/////////////////////////////////////////////////////////////////////////////////
typedef struct S_RP1210ProtocolInformation_t
{
	char *ProtocolDescription; ///< The description of the protocol.
	char *ProtocolSpeed;       ///< A string that represents the speed that the protocol can be used with.
	char *ProtocolString;      ///< The protocol string expected by RP1210_ClientConnect.
	char *ProtocolParams;      ///< Vendor specific protocol parameters.
	char *Devices;             ///< A comma delimited list of device ID's for devices that support this protocol.
}S_RP1210ProtocolInformation;

/////////////////////////////////////////////////////////////////////////////////
/// @brief Frees resources associated with an ORP_HANDLE.
///
/// This function should be used to free resources for any ORP_HANDLE returned
/// by an OpenRP1210 API function.
/// 
/// The handle is no longer valid after calling rpFreeHandle and should not be
/// used.
/// 
/// @param[in] handle The handle to free.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API void rpFreeHandle(ORP_HANDLE handle);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the last error set by an OpenRP1210 API function.
///
/// Some API functions will set LastError if an error is encountered. This
/// function is used to retrieve that error. Any functions that uses LastError
/// will set it to ORP_ERR_NO_ERROR first. 
/// 
/// If multiple threads are utilized, each thread will maintain its own LastError
/// value.
/// 
/// @return ORP_ERR_NO_ERROR if the last OpenRP1210 API function call was successful,
/// otherwise an ORP_ERR_* error code will be returned.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API ORP_ERR rpGetLastError(void);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets a string description of the last OpenRP1210 API error.
///
/// If there isn't an error (last error is ORP_ERR_NO_ERROR), then the string
/// "No error." will be returned.
/// 
/// @return A text description of the last error.
/// 
/// The returned char* is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API const char *rpGetLastErrorDesc(void);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Parses the RP1210 INI files and creates a handle that can be used
///        to get further information about the installed RP1210 devices. 
///
/// This function will parse RP1210.ini and the vendor INI files for all vendors
/// listed in RP1210.ini.
/// 
/// @return Returns NULL and sets LastError on failure or a valid handle on success. Even if a valid
///         handle is returned, it's still possible that there were errors parsing
///         INI files for specific vendors. Use rpGetNumApiImplLoadErrors to
///         determine if any vendor implementation INIs failed to load.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API ORP_HANDLE rpGetApiImpls(void);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets a handle to an RP1210 API implementation by index.
///
/// Use rpGetNumApiImpls to determine the range of valid implementation indices
/// [0, rpGetNumApiImpls(..) - 1]. 
/// 
/// @param[in] hImpls A handle to a collection of API implementations, as returned
///                   from rpGetApiImpls.
/// @param[in] index The index of the implementation to get.
/// @return A handle for the implementation or NULL on error. If NULL is returned,
///         LastError will be set for the error.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API ORP_HANDLE rpGetApiImpl(ORP_HANDLE hImpls, unsigned int index);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the number of errors that occurred when loading RP1210 INI 
///        information via rpGetApiImpls.
///
/// @param[in] hImpls A valid ORP_HANDLE returned from rpGetApiImpls.
/// @return Returns the number of errors.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API unsigned int rpGetNumApiImplLoadErrors(ORP_HANDLE hImpls);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets error details for an error that occurred when rpGetApiImpls
///        attempted to load RP1210 vendor INI information.
/// 
/// A valid index ranges from 0 to rpGetNumApiImplLoadErrors() - 1.
/// 
/// @param[in] hImpls A valid ORP_HANDLE returned from rpGetApiImpls.
/// @param[in] index The index of the error information.
/// @return The error details.
/// 
/// @note The returned S_RP1210ImplLoadErr is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API S_RP1210ImplLoadErr *rpGetApiImplLoadError(ORP_HANDLE hImpls, unsigned int index);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the number of RP1210 vendor APIs installed.
/// 
/// @param[in] hImpls A valid ORP_HANDLE returned from rpGetApiImpls.
/// @return Returns the number of RP1210 vendor APIs installed.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API unsigned int rpGetNumApiImpls(ORP_HANDLE hImpls);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the number of devices supported by a RP1210 vendor API.
/// 
/// An API implementation handle can be otained via various OpenRP1210 API
/// functions, for example, rpGetApiImplByName.
/// 
/// @param[in] hImpl A valid ORP_HANDLE referencing a API implementation. 
/// @return Returns the number of devices supported by the vendor API.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API unsigned int rpGetNumDevices(ORP_HANDLE hImpl);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the number of supported protocols by a RP1210 vendor API.
/// 
/// An API implementation handle can be otained via various OpenRP1210 API
/// functions, for example, rpGetApiImplByName.
/// 
/// @param[in] hImpl A valid ORP_HANDLE referencing a API implementation. 
/// @return Returns the number of protocols supported by the vendor API.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API unsigned int rpGetNumProtocols(ORP_HANDLE hImpl);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets a handle to an RP1210 API implementation.
/// 
/// @param[in] hImpls A valid ORP_HANDLE returned from rpGetApiImpls.
/// @param[in] name The name of the vendor API, as defined in the installed RP1210.ini file.
/// @return Returns a handle to the API implementation.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API ORP_HANDLE rpGetApiImplByName(ORP_HANDLE hImpls, const char *name);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the name of an RP1210 API implementation.
/// 
/// @param[in] hImpl A valid API implementation handle, obtained from rpGetApiImplByName,
///                  for example.
/// @return Returns the name of the API implementation.
/// 
/// @note The returned name is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API const char *rpGetApiImplName(ORP_HANDLE hImpl);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the path to the driver for an RP1210 API implementation.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @return Returns the path to the driver for the implementation.
/// 
/// @note The returned path is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API const char *rpGetDriverPath(ORP_HANDLE hImpl);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the vendor information, as described in the vendor INI file, for
///        a given RP1210 API implementation.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @return Returns the vendor information for the API implementation.
/// 
/// @note The returned S_RP1210DeviceInformation is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API S_RP1210VendorInformation *rpGetVendorInfo(ORP_HANDLE hImpl);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the device information, as described in the vendor INI file, for
///        a given RP1210 API implementation.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @param[in] index The index of the device. Valid indices are listed in the 
///                  vendor INI file for the device. They're also available in
///                  S_RP1210VendorInformation.
/// @return Returns the device information for the specified device. Returns NULL if no such
///         device exists.
/// 
/// @note The returned S_RP1210DeviceInformation is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API S_RP1210DeviceInformation *rpGetDeviceInfo(ORP_HANDLE hImpl, unsigned int index);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the device information, as described in the vendor INI file, for
///        a given RP1210 API implementation.
///
/// This function will get the DeviceInformation for the device with the given
/// name. However, it's possible that multiple devices may have the same name.
/// Therefore, deviceIndex allows a particular device to be specified.
/// 
/// For example, if there are two devices named "PCIcan", then setting deviceIndex
/// to 0 will retrieve the first while setting it to 1 will retrieve the 
/// second.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @param[in] name The name of the device whose DeviceInformation should be retrieved.
/// @param[in] deviceIndex The index of the device to retrieve. The first device is index 0.
/// @return Returns the device information for the specified device. Returns NULL if no such
///         device exists.
/// 
/// @note The returned S_RP1210DeviceInformation is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API S_RP1210DeviceInformation *rpGetDeviceInfoByName(ORP_HANDLE hImpl, const char *name, unsigned char deviceIndex);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the device information, as described in the vendor INI file, for
///        a given RP1210 API implementation.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @param[in] deviceId The device ID, as specified in the vendor INI file.
/// @return Returns the device information for the specified device. Returns NULL if no such
///         device exists.
/// 
/// @note The returned S_RP1210DeviceInformation is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API S_RP1210DeviceInformation *rpGetDeviceInfoById(ORP_HANDLE hImpl, unsigned int deviceId);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the DeviceId from an S_RP1210DeviceInformation.
///
/// This is a convenience function to get the deviceId as an int, which is generally
/// more useful than the char* with which DeviceId is stored as in S_RP1210DeviceInformation.
/// 
/// @param[in] deviceInfo The device info for the device whose DeviceId should be obtained.
/// @return Returns the device ID for the device.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API int rpGetDeviceId(S_RP1210DeviceInformation *deviceInfo);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets all devices that support the given protocol.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @param[in] protocol The name of a protocol.
/// @param[out] devices An array of devices that support the specified protocol will be placed here.
/// @param[out] numDevices This will be set to the number of S_RP1210DeviceInformation in devices.
/// @return Returns ORP_ERR_NO_ERROR on success.
/// 
/// @note devices is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API ORP_ERR rpGetDevicesByProtocol(ORP_HANDLE hImpl, const char *protocol, S_RP1210DeviceInformation **devices, unsigned int *numDevices);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the protocol information, as described in the vendor INI file, for
///        a given RP1210 API implementation.
/// 
/// The index is the index/id given in the vendor INI file. For example, given the following:
/// 
/// @code
/// [ProtocolInformation1]
/// ProtocolString = CAN
/// ProtocolDescription = Generic CAN Protocol
/// ProtocolSpeed = 125, 250, 500, 1000, all
/// ProtocolParams =
/// Devices = 1, 2, 3, 4, 5, 10, 11, 12, 13, 20, 21, 30, 31
/// @endcode
/// 
/// The preceding protocol would be obtained by calling rpGetProtocolInfo(handle, 1).
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @param[in] index The protocol id/index, as given in the vendor INI file.
/// @return Returns the protocol information, or NULL on error.
/// 
/// @note On error, use rpGetLastError to obtain the failure reason.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API S_RP1210ProtocolInformation *rpGetProtocolInfo(ORP_HANDLE hImpl, unsigned int index);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the protocol information, as described in the vendor INI file, for
///        a given RP1210 API implementation.
/// 
/// The "name" of the protocol maps to the "ProtocolString" in the vendor INI file.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @param[in] name The name of the protocol to get.
/// @return Returns the protocol information. Returns NULL if the protocol does exist or an error occurred.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API S_RP1210ProtocolInformation *rpGetProtocolInfoByName(ORP_HANDLE hImpl, const char *name);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets the protocol information, as described in the vendor INI file, for
///        a given RP1210 API implementation.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @param[in] id The ID of the protocol.
/// @return Returns the protocol information. Returns NULL if the protocol does exist or an error occurred.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API S_RP1210ProtocolInformation *rpGetProtocolInfoById(ORP_HANDLE hImpl, unsigned int id);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets all protocols supported by a given device.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @param[in] deviceInfo The device to get supported protocols for.
/// @param[out] protocols An array of protocols supported by the specified device will be placed here.
/// @param[out] numProtocols This will be set to the number of S_RP1210ProtocolInformation placed in protocols.
/// @return Returns ORP_ERR_NO_ERROR on success.
/// 
/// @note protocols is managed the API and should not be freed by the caller.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API ORP_ERR rpGetProtocolInfoByDevice(ORP_HANDLE hImpl, 
	                                                  S_RP1210DeviceInformation *deviceInfo, 
	                                                  S_RP1210ProtocolInformation **protocols, 
	                                                  unsigned int *numProtocols);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Gets a context for the give API implementation represented by a handle.
///
/// The S_RP1210Context can be used to call RP1210 functions from the vendor API.
/// 
/// @param[in] hImpl A valid API implementation handle.
/// @return An RP1210 context for the vendor API, or NULL on error.
/// 
/// @note The context returned should be released with rpReleaseContext when it
///       it is no longer needed.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API struct S_RP1210Context *rpGetContext(ORP_HANDLE hImpl);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Releases a context obtained via rpGetContext.
/// 
/// @param[in] context A context obtained via rpGetContext.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API void rpReleaseContext(struct S_RP1210Context *context);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Load a context for the given RP1210 API implementation into the global
///        context.
///
/// The global context is what is used when directly calling RP1210 functions
/// RP1210_ClientConnect, etc.
/// 
/// @param[in] hImpl A valid API implementation handle.
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API void rpLoadContext(ORP_HANDLE hImpl);

/////////////////////////////////////////////////////////////////////////////////
/// @brief Clears the global context loaded via rpLoadContext. 
/////////////////////////////////////////////////////////////////////////////////
CLINK OpenRP1210API void rpClearContext(void);

#include "RP1210.h"
#endif
